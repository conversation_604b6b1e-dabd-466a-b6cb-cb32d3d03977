import React from "react";
import { Line52 } from "../Line52/Line52";
import { Line53 } from "../Line53/Line53";
import "./style.css";

export default function Details ()  {
  return (
    <div className="frame">
      <div className="div">
        <div className="div-2">
          <div className="detailed-view">DETAILED VIEW</div>

          <div className="group">
            <div className="info-icon">
              <div className="info-circle">question-circle</div>
            </div>
          </div>
        </div>

        <div className="text-wrapper">times</div>
      </div>

      <div className="div-3">
        <div className="div-4">
          <div className="text-wrapper-2">Lorem ip:</div>

          <div className="text-wrapper-3">User 112215</div>
        </div>

        <div className="div-5">
          <div className="donec-pede">Donec pede:</div>

          <div className="text-wrapper-4">User 001</div>
        </div>

        <div className="group-2">
          <div className="text-wrapper-5"> Aeneanmodo modo :</div>

          <div className="text-wrapper-6">GCW 1</div>
        </div>
      </div>

      <div className="group-3">
        <div className="text-wrapper-7">Tiumquis:</div>

        <p className="p">ABC - DEF - GHI - KLM</p>
      </div>

      <Line52 className="line" />
      <div className="group-4">
        <div className="overlap-group">
          <p className="underlined">
            <span className="span">Underlined</span>
          </p>

          <div className="text-wrapper-8">:</div>
        </div>

        <div className="text-wrapper-9">Recent Change</div>

        <div className="text-wrapper-10">Legends:</div>
      </div>

      <Line53 className="line" />
      <p className="text-wrapper-11">Last Updated: 2025-01-30 02:20:39 AM</p>

      <div className="div-6">
        <div className="group-5">
          <div className="accordion-large">
            <div className="div-7">
              <p className="accordion-title"> Nullam dictum felis eu pede</p>

              <div className="div-8">
                <div className="text-wrapper-12">chevron-up</div>
              </div>
            </div>

            <div className="text-test-wrapper">
              <div className="text-test">
                <div className="text-wrapper-13">Data</div>
              </div>
            </div>
          </div>

          <div className="overlap-group-2">
            <div className="div-9">
              <div className="text-wrapper-14">Cum sociis natoque</div>

              <div className="text-wrapper-15">AACCGB21XXX</div>
            </div>

            <div className="div-10">
              <div className="text-wrapper-14">Contact Name:</div>

              <div className="text-wrapper-15">Stan Smith</div>
            </div>

            <div className="div-11">
              <div className="text-wrapper-14">Donec pede justo</div>

              <p className="text-wrapper-15">
                AQ CLEARING AND EXECUTION SERVICES LIMITED
              </p>
            </div>

            <div className="div-12">
              <div className="text-wrapper-14">Contact Phone:</div>

              <div className="text-wrapper-15">Not Available</div>
            </div>
          </div>
        </div>

        <div className="group-6">
          <div className="accordion-large">
            <div className="div-7">
              <div className="accordion-title">Massa Cum sociis natoque</div>

              <div className="div-8">
                <div className="text-wrapper-12">chevron-up</div>
              </div>
            </div>

            <div className="text-test-wrapper">
              <div className="text-test">
                <div className="text-wrapper-13">Data</div>
              </div>
            </div>
          </div>

          <div className="overlap">
            <div className="div-9">
              <div className="text-wrapper-14">Delivery Agent BIC</div>

              <div className="text-wrapper-15">AACCGB21XXX</div>
            </div>

            <div className="div-10">
              <div className="text-wrapper-14">City</div>

              <div className="text-wrapper-15">00123</div>
            </div>

            <div className="div-13">
              <div className="text-wrapper-14">Post Code</div>

              <div className="text-wrapper-15">00123</div>
            </div>

            <div className="div-12">
              <div className="text-wrapper-14">State/Province</div>

              <div className="text-wrapper-15">00123</div>
            </div>

            <div className="div-14">
              <div className="text-wrapper-14">Country</div>

              <div className="text-wrapper-15">00123</div>
            </div>

            <div className="div-11">
              <div className="text-wrapper-14">Delivery Agent Name</div>

              <div className="text-wrapper-15">Not Available</div>
            </div>

            <div className="div-15">
              <div className="text-wrapper-14">Delivery Agent Address</div>

              <div className="text-wrapper-15">Not Available</div>
            </div>
          </div>
        </div>

        <div className="group-7">
          <div className="accordion-large-2">
            <div className="div-16">
              <div className="accordion-title">Nullam Dictum felis</div>

              <div className="div-8">
                <div className="text-wrapper-12">chevron-up</div>
              </div>
            </div>

            <div className="div-wrapper">
              <div className="text-test">
                <div className="text-wrapper-13">Data</div>
              </div>
            </div>
          </div>

          <div className="overlap-2">
            <div className="text-wrapper-16">
              (For Settlement Management clients)
            </div>

            <div className="div-10">
              <div className="text-wrapper-14">Donec pede justo</div>

              <div className="text-wrapper-15">AACCGB21XXX</div>
            </div>

            <div className="div-12">
              <div className="text-wrapper-14">Lorem ipsum dolor sit</div>

              <div className="text-wrapper-15">AACCGB21XXX</div>
            </div>
          </div>
        </div>

        <div className="group-6">
          <div className="accordion-large">
            <div className="div-7">
              <div className="accordion-title">Aenean Vulputate</div>

              <div className="div-8">
                <div className="text-wrapper-12">chevron-up</div>
              </div>
            </div>

            <div className="text-test-wrapper">
              <div className="text-test">
                <div className="text-wrapper-13">Data</div>
              </div>
            </div>
          </div>

          <div className="overlap">
            <div className="div-9">
              <div className="text-wrapper-14">Delivery Agent BIC</div>

              <div className="text-wrapper-15">AACCGB21XXX</div>
            </div>

            <div className="div-10">
              <div className="text-wrapper-14">City</div>

              <div className="text-wrapper-15">00123</div>
            </div>

            <div className="div-13">
              <div className="text-wrapper-14">Post Code</div>

              <div className="text-wrapper-15">00123</div>
            </div>

            <div className="div-12">
              <div className="text-wrapper-14">State/Province</div>

              <div className="text-wrapper-15">00123</div>
            </div>

            <div className="div-14">
              <div className="text-wrapper-14">Country</div>

              <div className="text-wrapper-15">00123</div>
            </div>

            <div className="div-11">
              <div className="text-wrapper-14">Delivery Agent Name</div>

              <div className="text-wrapper-15">Not Available</div>
            </div>

            <div className="div-15">
              <div className="text-wrapper-14">Delivery Agent Address</div>

              <div className="text-wrapper-15">Not Available</div>
            </div>
          </div>
        </div>

        <div className="group-5">
          <div className="accordion-large">
            <div className="div-7">
              <div className="accordion-title">Aenean Vulputate eleifend</div>

              <div className="div-8">
                <div className="text-wrapper-12">chevron-up</div>
              </div>
            </div>

            <div className="text-test-wrapper">
              <div className="text-test">
                <div className="text-wrapper-13">Data</div>
              </div>
            </div>
          </div>

          <div className="overlap-group-2">
            <div className="div-9">
              <div className="text-wrapper-14">Lorem ipsum dolor sit :</div>

              <div className="text-wrapper-15">AACCGB21XXX</div>
            </div>

            <div className="div-10">
              <div className="text-wrapper-14">Contact Name:</div>

              <div className="text-wrapper-15">Stan Smith</div>
            </div>

            <div className="div-11">
              <div className="text-wrapper-14">Aenean vulputate eleifen:</div>

              <p className="text-wrapper-15">
                AQ CLEARING AND EXECUTION SERVICES LIMITED
              </p>
            </div>

            <div className="div-12">
              <div className="text-wrapper-14">Contact Phone:</div>

              <div className="text-wrapper-15">Not Available</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
