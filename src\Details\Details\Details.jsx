import React, { useState } from "react";
import { Line52 } from "../Line52/Line52";
import { Line53 } from "../Line53/Line53";
import {
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Typography
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  HelpOutline as HelpOutlineIcon,
  Close as CloseIcon
} from '@mui/icons-material';
import "./style.css";

export default function Details ()  {
  const [expandedAccordions, setExpandedAccordions] = useState({
    accordion1: false,
    accordion2: false,
    accordion3: false,
    accordion4: false,
    accordion5: false
  });

  const handleAccordionChange = (accordionId) => (event, isExpanded) => {
    setExpandedAccordions(prev => ({
      ...prev,
      [accordionId]: isExpanded
    }));
  };

  return (
    <div className="frame">
      <div className="div">
        <div className="div-2">
          <div className="detailed-view">DETAILED VIEW</div>

          <div className="group">
            <div className="info-icon">
              <HelpOutlineIcon sx={{ color: '#0e5447', fontSize: 16 }} />
            </div>
          </div>
        </div>

        <div className="text-wrapper">
          <CloseIcon sx={{ color: '#616161', fontSize: 20 }} />
        </div>
      </div>

      <div className="div-3">
        <div className="div-4">
          <div className="text-wrapper-2">Lorem ip:</div>

          <div className="text-wrapper-3">User 112215</div>
        </div>

        <div className="div-5">
          <div className="donec-pede">Donec pede:</div>

          <div className="text-wrapper-4">User 001</div>
        </div>

        <div className="group-2">
          <div className="text-wrapper-5"> Aeneanmodo modo :</div>

          <div className="text-wrapper-6">GCW 1</div>
        </div>
      </div>

      <div className="group-3">
        <div className="text-wrapper-7">Tiumquis:</div>

        <p className="p">ABC - DEF - GHI - KLM</p>
      </div>

      <Line52 className="line" />
      <div className="group-4">
        <div className="overlap-group">
          <p className="underlined">
            <span className="span">Underlined</span>
          </p>

          <div className="text-wrapper-8">:</div>
        </div>

        <div className="text-wrapper-9">Recent Change</div>

        <div className="text-wrapper-10">Legends:</div>
      </div>

      <Line53 className="line" />
      <p className="text-wrapper-11">Last Updated: 2025-01-30 02:20:39 AM</p>

      <div className="div-6">
        <Accordion
          expanded={expandedAccordions.accordion1}
          onChange={handleAccordionChange('accordion1')}
          className="custom-accordion"
          disableGutters
          elevation={0}
        >
          <AccordionSummary
            expandIcon={<ExpandMoreIcon sx={{ color: '#0e5447' }} />}
            className="accordion-summary"
          >
            <Typography className="accordion-title">
              Nullam dictum felis eu pede
            </Typography>
          </AccordionSummary>
          <AccordionDetails className="accordion-details">
            <div className="accordion-content">
              <div className="div-9">
                <div className="text-wrapper-14">Cum sociis natoque</div>
                <div className="text-wrapper-15">AACCGB21XXX</div>
              </div>

              <div className="div-10">
                <div className="text-wrapper-14">Contact Name:</div>
                <div className="text-wrapper-15">Stan Smith</div>
              </div>

              <div className="div-11">
                <div className="text-wrapper-14">Donec pede justo</div>
                <p className="text-wrapper-15">
                  AQ CLEARING AND EXECUTION SERVICES LIMITED
                </p>
              </div>

              <div className="div-12">
                <div className="text-wrapper-14">Contact Phone:</div>
                <div className="text-wrapper-15">Not Available</div>
              </div>
            </div>
          </AccordionDetails>
        </Accordion>

        <Accordion
          expanded={expandedAccordions.accordion2}
          onChange={handleAccordionChange('accordion2')}
          className="custom-accordion"
          disableGutters
          elevation={0}
        >
          <AccordionSummary
            expandIcon={<ExpandMoreIcon sx={{ color: '#0e5447' }} />}
            className="accordion-summary"
          >
            <Typography className="accordion-title">
              Massa Cum sociis natoque
            </Typography>
          </AccordionSummary>
          <AccordionDetails className="accordion-details">
            <div className="accordion-content">
              <div className="div-9">
                <div className="text-wrapper-14">Delivery Agent BIC</div>
                <div className="text-wrapper-15">AACCGB21XXX</div>
              </div>

              <div className="div-10">
                <div className="text-wrapper-14">City</div>
                <div className="text-wrapper-15">00123</div>
              </div>

              <div className="div-13">
                <div className="text-wrapper-14">Post Code</div>
                <div className="text-wrapper-15">00123</div>
              </div>

              <div className="div-12">
                <div className="text-wrapper-14">State/Province</div>
                <div className="text-wrapper-15">00123</div>
              </div>

              <div className="div-14">
                <div className="text-wrapper-14">Country</div>
                <div className="text-wrapper-15">00123</div>
              </div>

              <div className="div-11">
                <div className="text-wrapper-14">Delivery Agent Name</div>
                <div className="text-wrapper-15">Not Available</div>
              </div>

              <div className="div-15">
                <div className="text-wrapper-14">Delivery Agent Address</div>
                <div className="text-wrapper-15">Not Available</div>
              </div>
            </div>
          </AccordionDetails>
        </Accordion>

        <Accordion
          expanded={expandedAccordions.accordion3}
          onChange={handleAccordionChange('accordion3')}
          className="custom-accordion"
          disableGutters
          elevation={0}
        >
          <AccordionSummary
            expandIcon={<ExpandMoreIcon sx={{ color: '#0e5447' }} />}
            className="accordion-summary"
          >
            <Typography className="accordion-title">
              Nullam Dictum felis
            </Typography>
          </AccordionSummary>
          <AccordionDetails className="accordion-details">
            <div className="accordion-content">
              <div className="text-wrapper-16">
                (For Settlement Management clients)
              </div>

              <div className="div-10">
                <div className="text-wrapper-14">Donec pede justo</div>
                <div className="text-wrapper-15">AACCGB21XXX</div>
              </div>

              <div className="div-12">
                <div className="text-wrapper-14">Lorem ipsum dolor sit</div>
                <div className="text-wrapper-15">AACCGB21XXX</div>
              </div>
            </div>
          </AccordionDetails>
        </Accordion>

        <Accordion
          expanded={expandedAccordions.accordion4}
          onChange={handleAccordionChange('accordion4')}
          className="custom-accordion"
          disableGutters
          elevation={0}
        >
          <AccordionSummary
            expandIcon={<ExpandMoreIcon sx={{ color: '#0e5447' }} />}
            className="accordion-summary"
          >
            <Typography className="accordion-title">
              Aenean Vulputate
            </Typography>
          </AccordionSummary>
          <AccordionDetails className="accordion-details">
            <div className="accordion-content">
              <div className="div-9">
                <div className="text-wrapper-14">Delivery Agent BIC</div>
                <div className="text-wrapper-15">AACCGB21XXX</div>
              </div>

              <div className="div-10">
                <div className="text-wrapper-14">City</div>
                <div className="text-wrapper-15">00123</div>
              </div>

              <div className="div-13">
                <div className="text-wrapper-14">Post Code</div>
                <div className="text-wrapper-15">00123</div>
              </div>

              <div className="div-12">
                <div className="text-wrapper-14">State/Province</div>
                <div className="text-wrapper-15">00123</div>
              </div>

              <div className="div-14">
                <div className="text-wrapper-14">Country</div>
                <div className="text-wrapper-15">00123</div>
              </div>

              <div className="div-11">
                <div className="text-wrapper-14">Delivery Agent Name</div>
                <div className="text-wrapper-15">Not Available</div>
              </div>

              <div className="div-15">
                <div className="text-wrapper-14">Delivery Agent Address</div>
                <div className="text-wrapper-15">Not Available</div>
              </div>
            </div>
          </AccordionDetails>
        </Accordion>

        <Accordion
          expanded={expandedAccordions.accordion5}
          onChange={handleAccordionChange('accordion5')}
          className="custom-accordion"
          disableGutters
          elevation={0}
        >
          <AccordionSummary
            expandIcon={<ExpandMoreIcon sx={{ color: '#0e5447' }} />}
            className="accordion-summary"
          >
            <Typography className="accordion-title">
              Aenean Vulputate eleifend
            </Typography>
          </AccordionSummary>
          <AccordionDetails className="accordion-details">
            <div className="accordion-content">


              <div className="div-9">
                <div className="text-wrapper-14">Lorem ipsum dolor sit :</div>
                <div className="text-wrapper-15">AACCGB21XXX</div>
              </div>

              <div className="div-10">
                <div className="text-wrapper-14">Contact Name:</div>
                <div className="text-wrapper-15">Stan Smith</div>
              </div>

              <div className="div-11">
                <div className="text-wrapper-14">Aenean vulputate eleifen:</div>
                <p className="text-wrapper-15">
                  AQ CLEARING AND EXECUTION SERVICES LIMITED
                </p>
              </div>

              <div className="div-12">
                <div className="text-wrapper-14">Contact Phone:</div>
                <div className="text-wrapper-15">Not Available</div>
              </div>
            </div>
          </AccordionDetails>
        </Accordion>
      </div>
    </div>
  );
};
